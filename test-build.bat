@echo off
echo ========================================
echo   اختبار بناء المشروع
echo ========================================
echo.

echo الخطوة 1: إعداد PATH...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo الخطوة 2: الانتقال لمجلد المشروع...
cd AlNoorEducationalInstitute

echo الخطوة 3: حذف ملفات البناء القديمة...
if exist "obj" rmdir /s /q obj
if exist "bin" rmdir /s /q bin

echo الخطوة 4: استعادة الحزم...
dotnet restore

echo الخطوة 5: اختبار البناء...
dotnet build --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ نجح البناء! المشروع جاهز للنشر
    echo.
    echo تشغيل النشر...
    dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo   نجح! التطبيق جاهز
        echo ========================================
        echo.
        echo الموقع: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
        echo.
        echo بيانات الدخول:
        echo المستخدم: admin
        echo كلمة المرور: admin123
    )
) else (
    echo.
    echo ✗ فشل البناء - تحقق من الأخطاء أعلاه
)

cd ..
echo.
pause
