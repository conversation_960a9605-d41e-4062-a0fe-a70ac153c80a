using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الموظف
    /// Employee data model
    /// </summary>
    public class Employee
    {
        /// <summary>
        /// المعرف الفريد للموظف
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// الرقم الوظيفي للموظف
        /// </summary>
        [Required(ErrorMessage = "الرقم الوظيفي مطلوب")]
        [StringLength(20, ErrorMessage = "الرقم الوظيفي يجب أن يكون أقل من 20 حرف")]
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل باللغة العربية
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
        public string FullNameArabic { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل باللغة الإنجليزية (اختياري)
        /// </summary>
        [StringLength(100, ErrorMessage = "الاسم الإنجليزي يجب أن يكون أقل من 100 حرف")]
        public string? FullNameEnglish { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        [Required(ErrorMessage = "تاريخ الميلاد مطلوب")]
        public DateTime DateOfBirth { get; set; }

        /// <summary>
        /// مكان الميلاد
        /// </summary>
        [StringLength(100, ErrorMessage = "مكان الميلاد يجب أن يكون أقل من 100 حرف")]
        public string? PlaceOfBirth { get; set; }

        /// <summary>
        /// الجنس (ذكر/أنثى)
        /// </summary>
        [Required(ErrorMessage = "الجنس مطلوب")]
        public Gender Gender { get; set; }

        /// <summary>
        /// الجنسية
        /// </summary>
        [StringLength(50, ErrorMessage = "الجنسية يجب أن تكون أقل من 50 حرف")]
        public string? Nationality { get; set; }

        /// <summary>
        /// رقم الهوية الوطنية أو جواز السفر
        /// </summary>
        [Required(ErrorMessage = "رقم الهوية مطلوب")]
        [StringLength(30, ErrorMessage = "رقم الهوية يجب أن يكون أقل من 30 حرف")]
        public string NationalId { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف الأساسي
        /// </summary>
        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 رقم")]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف الثانوي (اختياري)
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف الثانوي يجب أن يكون أقل من 20 رقم")]
        public string? Phone2 { get; set; }

        /// <summary>
        /// رقم الهاتف (alias لـ Phone للتوافق مع الكود)
        /// </summary>
        public string PhoneNumber
        {
            get => Phone;
            set => Phone = value;
        }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// العنوان السكني
        /// </summary>
        [Required(ErrorMessage = "العنوان السكني مطلوب")]
        [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// المؤهل العلمي
        /// </summary>
        [Required(ErrorMessage = "المؤهل العلمي مطلوب")]
        [StringLength(100, ErrorMessage = "المؤهل العلمي يجب أن يكون أقل من 100 حرف")]
        public string Qualification { get; set; } = string.Empty;

        /// <summary>
        /// التخصص
        /// </summary>
        [StringLength(100, ErrorMessage = "التخصص يجب أن يكون أقل من 100 حرف")]
        public string? Specialization { get; set; }

        /// <summary>
        /// سنوات الخبرة
        /// </summary>
        public int YearsOfExperience { get; set; }

        /// <summary>
        /// الخبرات السابقة
        /// </summary>
        [StringLength(500, ErrorMessage = "الخبرات السابقة يجب أن تكون أقل من 500 حرف")]
        public string? PreviousExperience { get; set; }

        /// <summary>
        /// المنصب الوظيفي
        /// </summary>
        [Required(ErrorMessage = "المنصب الوظيفي مطلوب")]
        public EmployeePosition Position { get; set; }

        /// <summary>
        /// القسم أو الإدارة
        /// </summary>
        [StringLength(100, ErrorMessage = "القسم يجب أن يكون أقل من 100 حرف")]
        public string? Department { get; set; }

        /// <summary>
        /// تاريخ التعيين
        /// </summary>
        [Required(ErrorMessage = "تاريخ التعيين مطلوب")]
        public DateTime HireDate { get; set; }

        /// <summary>
        /// الراتب الأساسي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الراتب يجب أن يكون أكبر من الصفر")]
        public decimal Salary { get; set; }

        /// <summary>
        /// حالة الموظف (نشط، معلق، مستقيل، مفصول)
        /// </summary>
        public EmployeeStatus Status { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم المرتبط بهذا الموظف (إذا كان له حساب في النظام)
        /// </summary>
        public int? UserId { get; set; }

        // Navigation Properties
        public virtual User? User { get; set; }
    }

    /// <summary>
    /// تعداد المنصب الوظيفي
    /// </summary>
    public enum EmployeePosition
    {
        Director = 1,           // مدير
        ViceDirector = 2,       // نائب مدير
        Teacher = 3,            // مدرس
        Administrator = 4,      // موظف إداري
        Accountant = 5,         // محاسب
        Receptionist = 6,       // موظف استقبال
        Librarian = 7,          // أمين مكتبة
        ITSpecialist = 8,       // أخصائي تقنية معلومات
        Counselor = 9,          // مرشد طلابي
        Nurse = 10,             // ممرض
        SecurityGuard = 11,     // حارس أمن
        Janitor = 12,           // عامل نظافة
        Driver = 13,            // سائق
        Other = 99              // أخرى
    }

    /// <summary>
    /// تعداد حالة الموظف
    /// </summary>
    public enum EmployeeStatus
    {
        Active = 1,         // نشط
        Suspended = 2,      // معلق
        Resigned = 3,       // مستقيل
        Terminated = 4,     // مفصول
        OnLeave = 5         // في إجازة
    }
}
