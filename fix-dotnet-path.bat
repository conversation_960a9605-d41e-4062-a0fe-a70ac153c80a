@echo off
echo ========================================
echo   Fixing .NET PATH Issue
echo ========================================
echo.

echo Step 1: Adding .NET to PATH for this session...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo Step 2: Testing dotnet command...
dotnet --version
if %ERRORLEVEL% EQU 0 (
    echo ✓ Success! .NET is now working
    echo.
    echo Step 3: Building the application...
    cd AlNoorEducationalInstitute
    dotnet build --configuration Release
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Build successful!
        echo.
        echo Step 4: Publishing the application...
        dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem
        if %ERRORLEVEL% EQU 0 (
            echo ✓ Publish successful!
            echo.
            echo Application published to: Published\AlNoorSystem\
            echo You can run: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
        ) else (
            echo ✗ Publish failed
        )
    ) else (
        echo ✗ Build failed
    )
    cd ..
) else (
    echo ✗ .NET still not working
    echo.
    echo Please add C:\Program Files\dotnet to your system PATH manually:
    echo 1. Open System Properties
    echo 2. Click Environment Variables
    echo 3. Edit PATH variable
    echo 4. Add: C:\Program Files\dotnet
    echo 5. Restart Command Prompt
)

echo.
pause
