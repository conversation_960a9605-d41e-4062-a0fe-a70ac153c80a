#!/usr/bin/env pwsh
# سكريبت لتحديث جميع مراجع SQLite من System.Data.SQLite إلى Microsoft.Data.Sqlite

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  تحديث مراجع SQLite" -ForegroundColor Cyan
Write-Host "  من System.Data.SQLite إلى Microsoft.Data.Sqlite" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# قائمة الملفات التي تحتاج تحديث
$filesToUpdate = @(
    "AlNoorEducationalInstitute\Data\DatabaseManager.cs",
    "AlNoorEducationalInstitute\Services\AuthenticationService.cs",
    "AlNoorEducationalInstitute\Services\StudentService.cs",
    "AlNoorEducationalInstitute\Services\EmployeeService.cs",
    "AlNoorEducationalInstitute\Services\ClassService.cs",
    "AlNoorEducationalInstitute\Services\SubjectService.cs",
    "AlNoorEducationalInstitute\Services\InstitutionService.cs",
    "AlNoorEducationalInstitute\Services\FeeService.cs",
    "AlNoorEducationalInstitute\Services\InvoiceService.cs",
    "AlNoorEducationalInstitute\Services\PaymentService.cs",
    "AlNoorEducationalInstitute\Services\GradeService.cs",
    "AlNoorEducationalInstitute\Services\AttendanceService.cs",
    "AlNoorEducationalInstitute\Services\ReportService.cs"
)

# التحديثات المطلوبة
$replacements = @{
    "using System.Data.SQLite;" = "using Microsoft.Data.Sqlite;"
    "SQLiteConnection" = "SqliteConnection"
    "SQLiteCommand" = "SqliteCommand"
    "SQLiteDataReader" = "SqliteDataReader"
    "SQLiteParameter" = "SqliteParameter"
    "SQLiteTransaction" = "SqliteTransaction"
    "SQLiteConnectionStringBuilder" = "SqliteConnectionStringBuilder"
}

$totalFiles = 0
$updatedFiles = 0

foreach ($file in $filesToUpdate) {
    if (Test-Path $file) {
        Write-Host "معالجة ملف: $file" -ForegroundColor Yellow
        $totalFiles++
        
        $content = Get-Content $file -Raw
        $originalContent = $content
        
        # تطبيق جميع التحديثات
        foreach ($old in $replacements.Keys) {
            $new = $replacements[$old]
            $content = $content -replace [regex]::Escape($old), $new
        }
        
        # حفظ الملف إذا تم تغييره
        if ($content -ne $originalContent) {
            Set-Content $file -Value $content -NoNewline
            Write-Host "✓ تم تحديث: $file" -ForegroundColor Green
            $updatedFiles++
        } else {
            Write-Host "- لا يحتاج تحديث: $file" -ForegroundColor Gray
        }
    } else {
        Write-Host "✗ ملف غير موجود: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  ملخص التحديث" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "إجمالي الملفات المعالجة: $totalFiles" -ForegroundColor White
Write-Host "الملفات المحدثة: $updatedFiles" -ForegroundColor Green
Write-Host "الملفات غير المحدثة: $($totalFiles - $updatedFiles)" -ForegroundColor Gray

if ($updatedFiles -gt 0) {
    Write-Host ""
    Write-Host "✓ تم تحديث مراجع SQLite بنجاح!" -ForegroundColor Green
    Write-Host "الخطوة التالية: تشغيل dotnet restore && dotnet build" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "لا توجد ملفات تحتاج تحديث" -ForegroundColor Gray
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
