# 🚀 دليل التشغيل السريع - نظام مؤسسة النور التربوي

## 🎉 تحديث: النظام محدث إلى .NET 9.0!

**المشروع الآن يستخدم .NET 9.0 (الأحدث والأسرع):**

### إذا لم يكن .NET 9.0 مثبت:
1. **ثبت**: .NET 9.0 SDK من الملف الذي حملته
2. **أو حمل**: من microsoft.com/dotnet
3. **ثبت** واعد تشغيل Command Prompt
4. **اختبر**: `dotnet --version` (يجب أن يظهر 9.0.302)

**📖 دليل التحديث**: `DOTNET9_UPDATE.md`

---

## طرق التشغيل (بعد تثبيت .NET 9.0)

### 🎯 الطريقة الأولى: التشغيل السريع (الأسهل)
```
1. ان<PERSON><PERSON> مرتين على: run-app.bat
2. انتظر حتى يكتمل النشر
3. سيفتح البرنامج تلقائياً
```

### 🎯 الطريقة الثانية: التثبيت المبسط
```
1. انقر مرتين على: simple-install.bat
2. اكتب Y واضغط Enter
3. انتظر حتى يكتمل التثبيت
4. ستجد اختصار على سطح المكتب
```

### 🎯 الطريقة الثالثة: Visual Studio (الأكثر موثوقية) ⭐
```
1. حمل Visual Studio Community (مجاني)
2. افتح ملف: AlNoorEducationalInstitute.sln
3. اضغط F5 أو انقر "Start"
4. سيعمل البرنامج مباشرة
```
**📖 دليل مفصل**: `run-with-visual-studio.md`

---

## 🔐 معلومات تسجيل الدخول

**المستخدم الافتراضي:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

⚠️ **مهم**: غيّر كلمة المرور بعد أول تسجيل دخول!

---

## 📁 ملفات مهمة

### ملفات التشغيل:
- `run-app.bat` ← تشغيل سريع
- `simple-install.bat` ← تثبيت مبسط
- `install.bat` ← تثبيت كامل (قد يحتاج إصلاح)

### ملفات المساعدة:
- `TROUBLESHOOTING.md` ← حل المشاكل
- `README_USER.md` ← دليل المستخدم الكامل
- `DEPLOYMENT_GUIDE.md` ← دليل المطور

---

## ❗ إذا واجهت مشاكل

### المشكلة: "dotnet غير معروف"
```
'dotnet' n'est pas reconnu en tant que commande interne
```
**الحل:**
1. **اقرأ**: `INSTALL_DOTNET.md` - دليل مفصل
2. **حمل**: .NET 6.0 SDK من https://dotnet.microsoft.com/download
3. **ثبت** واعد تشغيل الكمبيوتر
4. **اختبر**: `dotnet --version`

### المشكلة: أخطاء في التجميع أو مشاكل PATH
```
Build failed with errors
'dotnet' n'est pas reconnu
```
**الحل الأفضل:**
- **استخدم Visual Studio** (الطريقة الثالثة أعلاه)
- اقرأ `run-with-visual-studio.md` للتفاصيل
- Visual Studio يحل جميع مشاكل .NET تلقائياً

### المشكلة: نصوص غريبة في Command Prompt
**الحل:**
- استخدم `simple-install.bat` بدلاً من `install.bat`
- الملفات الجديدة تستخدم نصوص إنجليزية فقط

### المشكلة: لا يعمل أي شيء
**الحل:**
1. تأكد من Windows 10 أو أحدث
2. شغل Command Prompt كمدير
3. اقرأ ملف `TROUBLESHOOTING.md`

---

## 🎉 بعد التشغيل الناجح

### ستجد البرنامج في:
- سطح المكتب (إذا استخدمت المثبت)
- مجلد: `Published\AlNoorSystem\`
- قائمة ابدأ (إذا استخدمت التثبيت الكامل)

### الميزات المتاحة:
- ✅ إدارة الطلاب والموظفين
- ✅ النظام المالي والفواتير
- ✅ التقارير والإحصائيات
- ✅ إدارة الشعار والهوية البصرية
- ✅ النسخ الاحتياطي التلقائي

---

## 📞 تحتاج مساعدة؟

1. **اقرأ أولاً**: `TROUBLESHOOTING.md`
2. **للمستخدمين**: `README_USER.md`
3. **للمطورين**: `DEPLOYMENT_GUIDE.md`

---

## 🔄 التحديثات المستقبلية

عند توفر إصدار جديد:
1. احتفظ بنسخة احتياطية من قاعدة البيانات
2. حمل الإصدار الجديد
3. شغل المثبت الجديد
4. ستحتفظ ببياناتك السابقة

---

**نصيحة**: ابدأ بـ `run-app.bat` للتجربة السريعة! 🚀
