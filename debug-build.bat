@echo off
echo ========================================
echo   Debug Build - Capture Errors
echo ========================================
echo.

echo Setting up .NET PATH...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo Testing dotnet command...
dotnet --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: dotnet command not working
    pause
    exit /b 1
)

echo.
echo Changing to project directory...
cd AlNoorEducationalInstitute

echo.
echo Running clean build with detailed output...
dotnet clean --verbosity normal
dotnet build --verbosity normal --no-restore > build-output.txt 2>&1

echo.
echo Build completed. Checking results...
if %ERRORLEVEL% EQU 0 (
    echo ✓ Build successful!
) else (
    echo ✗ Build failed. Showing errors:
    echo.
    type build-output.txt
)

echo.
echo Build output saved to: AlNoorEducationalInstitute\build-output.txt
pause
