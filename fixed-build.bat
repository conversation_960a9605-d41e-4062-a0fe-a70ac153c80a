@echo off
echo ========================================
echo   Fixed Build - Updated Dependencies
echo ========================================
echo.

echo Step 1: Setting PATH...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo Step 2: Testing dotnet...
dotnet --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: dotnet not working
    pause
    exit /b 1
)

echo Step 3: Going to project directory...
cd AlNoorEducationalInstitute

echo Step 4: Cleaning previous build...
dotnet clean --verbosity quiet

echo Step 5: Restoring packages with updated versions...
dotnet restore --verbosity normal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)

echo Step 6: Building project...
dotnet build --no-restore --verbosity normal
if %ERRORLEVEL% EQU 0 (
    echo ✓ Build successful!
    echo.
    echo Step 7: Publishing...
    dotnet publish --no-build --configuration Release --output ..\Published\AlNoorSystem
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Publish successful!
        echo.
        echo ========================================
        echo   SUCCESS! Application is ready
        echo ========================================
        echo.
        echo Location: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
        echo.
        echo Default login:
        echo Username: admin
        echo Password: admin123
        echo.
    ) else (
        echo ✗ Publish failed
    )
) else (
    echo ✗ Build failed - checking for common issues...
    echo.
    echo Possible causes:
    echo 1. Missing using statements
    echo 2. Version compatibility issues
    echo 3. Missing model classes
    echo 4. Interface implementation issues
)

echo.
pause
