@echo off
echo ========================================
echo   Al-Noor Build Status Checker
echo ========================================
echo.

echo Checking if application was built successfully...
echo.

if exist "Published\AlNoorSystem\AlNoorEducationalInstitute.exe" (
    echo ✓ Application found at: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    echo.
    echo File details:
    dir "Published\AlNoorSystem\AlNoorEducationalInstitute.exe"
    echo.
    echo ========================================
    echo   APPLICATION IS READY TO RUN!
    echo ========================================
    echo.
    echo To run the application:
    echo 1. Double-click: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    echo 2. Or run from command line: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    echo.
    echo Default login:
    echo Username: admin
    echo Password: admin123
    echo.
) else (
    echo ✗ Application not found
    echo.
    echo Checking project structure...
    if exist "AlNoorEducationalInstitute\AlNoorEducationalInstitute.csproj" (
        echo ✓ Project file found
        echo.
        echo Current .NET version:
        dotnet --version 2>nul || echo "✗ .NET not found in PATH"
        echo.
        echo To build the application:
        echo 1. Run: build-net6.bat
        echo 2. Or run: PowerShell -ExecutionPolicy Bypass -File Build-AlNoor.ps1
        echo 3. Or follow manual steps in the instructions
    ) else (
        echo ✗ Project file not found
        echo Make sure you're in the correct directory
    )
)

echo.
pause
