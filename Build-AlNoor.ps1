#!/usr/bin/env pwsh
# Al-Noor Educational Institute Build Script
# PowerShell version for better compatibility

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Al-Noor Educational Institute" -ForegroundColor Cyan
Write-Host "  .NET 6.0 Compatible Build" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check .NET installation
Write-Host "Checking .NET installation..." -ForegroundColor Yellow
try {
    $dotnetVersion = & dotnet --version 2>$null
    Write-Host "✓ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET not found in PATH" -ForegroundColor Red
    Write-Host "Adding .NET to PATH..." -ForegroundColor Yellow
    $env:PATH += ";C:\Program Files\dotnet"
    
    try {
        $dotnetVersion = & dotnet --version 2>$null
        Write-Host "✓ .NET Version: $dotnetVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ .NET SDK not installed" -ForegroundColor Red
        Write-Host "Please install .NET 6.0 SDK from: https://dotnet.microsoft.com/download/dotnet/6.0" -ForegroundColor Yellow
        exit 1
    }
}

# Navigate to project directory
Write-Host ""
Write-Host "Navigating to project directory..." -ForegroundColor Yellow
Set-Location "AlNoorEducationalInstitute"

# Clean project
Write-Host ""
Write-Host "Step 1: Cleaning project..." -ForegroundColor Yellow
& dotnet clean --configuration Release --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Clean failed" -ForegroundColor Red
    exit 1
}
Write-Host "✓ Clean successful" -ForegroundColor Green

# Restore packages
Write-Host ""
Write-Host "Step 2: Restoring packages..." -ForegroundColor Yellow
& dotnet restore --verbosity minimal
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Package restore failed" -ForegroundColor Red
    exit 1
}
Write-Host "✓ Package restore successful" -ForegroundColor Green

# Build project
Write-Host ""
Write-Host "Step 3: Building project..." -ForegroundColor Yellow
& dotnet build --configuration Release --no-restore --verbosity minimal
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Build failed" -ForegroundColor Red
    Write-Host "Please check the error messages above." -ForegroundColor Yellow
    exit 1
}
Write-Host "✓ Build successful" -ForegroundColor Green

# Publish application
Write-Host ""
Write-Host "Step 4: Publishing application..." -ForegroundColor Yellow
& dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "..\Published\AlNoorSystem" --verbosity minimal

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "  SUCCESS! Application Published" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "📁 Location: Published\AlNoorSystem\AlNoorEducationalInstitute.exe" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🔑 Default login:" -ForegroundColor Yellow
    Write-Host "   Username: admin" -ForegroundColor White
    Write-Host "   Password: admin123" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 You can now run the application!" -ForegroundColor Green
} else {
    Write-Host "✗ Publish failed" -ForegroundColor Red
}

# Return to original directory
Set-Location ".."

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
