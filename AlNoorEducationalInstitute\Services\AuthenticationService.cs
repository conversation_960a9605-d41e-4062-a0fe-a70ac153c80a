using System;
using Microsoft.Data.Sqlite;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using BCrypt.Net;
using AlNoorEducationalInstitute.Data;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// خدمة المصادقة
    /// Authentication service
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly DatabaseManager _databaseManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthenticationService> _logger;
        private readonly int _maxLoginAttempts;
        private readonly int _lockoutDurationMinutes;

        public AuthenticationService(
            DatabaseManager databaseManager, 
            IConfiguration configuration, 
            ILogger<AuthenticationService> logger)
        {
            _databaseManager = databaseManager;
            _configuration = configuration;
            _logger = logger;
            _maxLoginAttempts = _configuration.GetValue<int>("ApplicationSettings:MaxLoginAttempts", 3);
            _lockoutDurationMinutes = _configuration.GetValue<int>("Security:AccountLockoutDuration", 15);
        }

        public async Task<AuthenticationResult> LoginAsync(string username, string password, string? ipAddress = null, string? userAgent = null)
        {
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                // البحث عن المستخدم
                var user = await GetUserByUsernameAsync(connection, username);
                if (user == null)
                {
                    await LogSecurityEventAsync(SecurityEventType.LoginFailure, null, username, ipAddress, userAgent, "اسم المستخدم غير موجود");
                    return new AuthenticationResult
                    {
                        Success = false,
                        ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }

                // التحقق من حالة المستخدم
                if (user.Status != UserStatus.Active)
                {
                    await LogSecurityEventAsync(SecurityEventType.LoginFailure, user.UserId, username, ipAddress, userAgent, $"المستخدم غير نشط - الحالة: {user.Status}");
                    return new AuthenticationResult
                    {
                        Success = false,
                        ErrorMessage = "الحساب غير نشط. يرجى الاتصال بالمدير"
                    };
                }

                // التحقق من قفل الحساب
                if (await IsAccountLockedAsync(user.UserId))
                {
                    await LogSecurityEventAsync(SecurityEventType.LoginFailure, user.UserId, username, ipAddress, userAgent, "محاولة دخول لحساب مقفل");
                    return new AuthenticationResult
                    {
                        Success = false,
                        IsAccountLocked = true,
                        LockoutEndTime = user.AccountLockedUntil,
                        ErrorMessage = $"الحساب مقفل حتى {user.AccountLockedUntil:yyyy-MM-dd HH:mm}"
                    };
                }

                // التحقق من كلمة المرور
                if (!VerifyPassword(password, user.PasswordHash))
                {
                    await RecordFailedLoginAttemptAsync(user.UserId);
                    
                    var remainingAttempts = _maxLoginAttempts - user.FailedLoginAttempts - 1;
                    
                    if (remainingAttempts <= 0)
                    {
                        await LockUserAccountAsync(user.UserId, DateTime.Now.AddMinutes(_lockoutDurationMinutes), user.UserId);
                        await LogSecurityEventAsync(SecurityEventType.AccountLocked, user.UserId, username, ipAddress, userAgent, "تم قفل الحساب بسبب تجاوز عدد محاولات الدخول المسموحة");
                        
                        return new AuthenticationResult
                        {
                            Success = false,
                            IsAccountLocked = true,
                            LockoutEndTime = DateTime.Now.AddMinutes(_lockoutDurationMinutes),
                            ErrorMessage = "تم قفل الحساب بسبب تجاوز عدد محاولات الدخول المسموحة"
                        };
                    }

                    await LogSecurityEventAsync(SecurityEventType.LoginFailure, user.UserId, username, ipAddress, userAgent, "كلمة مرور خاطئة");
                    
                    return new AuthenticationResult
                    {
                        Success = false,
                        RemainingAttempts = remainingAttempts,
                        ErrorMessage = $"كلمة المرور غير صحيحة. المحاولات المتبقية: {remainingAttempts}"
                    };
                }

                // التحقق من انتهاء صلاحية كلمة المرور
                var isPasswordExpired = await IsPasswordExpiredAsync(user.UserId);

                // نجح تسجيل الدخول
                await ResetFailedLoginAttemptsAsync(user.UserId);
                await UpdateLastLoginDateAsync(user.UserId);
                
                await LogSecurityEventAsync(SecurityEventType.LoginSuccess, user.UserId, username, ipAddress, userAgent);

                return new AuthenticationResult
                {
                    Success = true,
                    User = user,
                    IsPasswordExpired = isPasswordExpired,
                    MustChangePassword = user.MustChangePassword || isPasswordExpired
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عملية تسجيل الدخول للمستخدم {Username}", username);
                throw;
            }
        }

        public async Task<bool> LogoutAsync(int userId)
        {
            try
            {
                await LogSecurityEventAsync(SecurityEventType.Logout, userId, null, null, null);
                _logger.LogInformation("تم تسجيل خروج المستخدم {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل خروج المستخدم {UserId}", userId);
                return false;
            }
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من كلمة المرور");
                return false;
            }
        }

        public string HashPassword(string password)
        {
            try
            {
                return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تشفير كلمة المرور");
                throw;
            }
        }

        public PasswordStrengthResult ValidatePasswordStrength(string password)
        {
            var result = new PasswordStrengthResult();
            var issues = new List<string>();
            var suggestions = new List<string>();
            var score = 0;

            var minLength = _configuration.GetValue<int>("Security:PasswordMinLength", 8);
            var requireSpecialChars = _configuration.GetValue<bool>("Security:RequireSpecialCharacters", true);
            var requireNumbers = _configuration.GetValue<bool>("Security:RequireNumbers", true);
            var requireUppercase = _configuration.GetValue<bool>("Security:RequireUppercase", true);
            var requireLowercase = _configuration.GetValue<bool>("Security:RequireLowercase", true);

            // فحص الطول
            if (password.Length < minLength)
            {
                issues.Add($"كلمة المرور يجب أن تكون على الأقل {minLength} أحرف");
                suggestions.Add("استخدم كلمة مرور أطول");
            }
            else
            {
                score += 20;
            }

            // فحص الأحرف الكبيرة
            if (requireUppercase && !password.Any(char.IsUpper))
            {
                issues.Add("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل");
                suggestions.Add("أضف حروف كبيرة (A-Z)");
            }
            else if (password.Any(char.IsUpper))
            {
                score += 20;
            }

            // فحص الأحرف الصغيرة
            if (requireLowercase && !password.Any(char.IsLower))
            {
                issues.Add("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل");
                suggestions.Add("أضف حروف صغيرة (a-z)");
            }
            else if (password.Any(char.IsLower))
            {
                score += 20;
            }

            // فحص الأرقام
            if (requireNumbers && !password.Any(char.IsDigit))
            {
                issues.Add("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل");
                suggestions.Add("أضف أرقام (0-9)");
            }
            else if (password.Any(char.IsDigit))
            {
                score += 20;
            }

            // فحص الرموز الخاصة
            var specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
            if (requireSpecialChars && !password.Any(c => specialChars.Contains(c)))
            {
                issues.Add("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل");
                suggestions.Add("أضف رموز خاصة (!@#$%^&*)");
            }
            else if (password.Any(c => specialChars.Contains(c)))
            {
                score += 20;
            }

            result.IsValid = issues.Count == 0;
            result.Score = score;
            result.Issues = issues;
            result.Suggestions = suggestions;

            return result;
        }

        public async Task<bool> IsPasswordExpiredAsync(int userId)
        {
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT PasswordExpiryDate FROM Users WHERE UserId = @UserId";
                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@UserId", userId);

                var result = await command.ExecuteScalarAsync();
                if (result == null || result == DBNull.Value)
                    return false;

                var expiryDate = Convert.ToDateTime(result);
                return DateTime.Now > expiryDate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص انتهاء صلاحية كلمة المرور للمستخدم {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> IsAccountLockedAsync(int userId)
        {
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT AccountLockedUntil FROM Users WHERE UserId = @UserId";
                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@UserId", userId);

                var result = await command.ExecuteScalarAsync();
                if (result == null || result == DBNull.Value)
                    return false;

                var lockUntil = Convert.ToDateTime(result);
                return DateTime.Now < lockUntil;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص قفل الحساب للمستخدم {UserId}", userId);
                return false;
            }
        }

        // باقي الدوال سيتم إضافتها في الجزء التالي...
        
        private async Task<User?> GetUserByUsernameAsync(SQLiteConnection connection, string username)
        {
            var sql = "SELECT * FROM Users WHERE Username = @Username";
            using var command = new SQLiteCommand(sql, connection);
            command.Parameters.AddWithValue("@Username", username);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapReaderToUser(reader);
            }

            return null;
        }

        private User MapReaderToUser(SQLiteDataReader reader)
        {
            return new User
            {
                UserId = reader.GetInt32("UserId"),
                Username = reader.GetString("Username"),
                PasswordHash = reader.GetString("PasswordHash"),
                FullName = reader.GetString("FullName"),
                Email = reader.GetString("Email"),
                Phone = reader.IsDBNull("Phone") ? null : reader.GetString("Phone"),
                Role = (UserRole)reader.GetInt32("Role"),
                Permissions = reader.IsDBNull("Permissions") ? null : reader.GetString("Permissions"),
                Status = (UserStatus)reader.GetInt32("Status"),
                LastLoginDate = reader.IsDBNull("LastLoginDate") ? null : reader.GetDateTime("LastLoginDate"),
                FailedLoginAttempts = reader.GetInt32("FailedLoginAttempts"),
                AccountLockedUntil = reader.IsDBNull("AccountLockedUntil") ? null : reader.GetDateTime("AccountLockedUntil"),
                PasswordExpiryDate = reader.IsDBNull("PasswordExpiryDate") ? null : reader.GetDateTime("PasswordExpiryDate"),
                MustChangePassword = reader.GetBoolean("MustChangePassword"),
                EmployeeId = reader.IsDBNull("EmployeeId") ? null : reader.GetInt32("EmployeeId"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                LastModifiedDate = reader.GetDateTime("LastModifiedDate"),
                CreatedByUserId = reader.GetInt32("CreatedByUserId"),
                LastModifiedByUserId = reader.GetInt32("LastModifiedByUserId")
            };
        }

        // سيتم إضافة باقي الدوال في الجزء التالي
        public Task<bool> HasPermissionAsync(int userId, string permission) => throw new NotImplementedException();
        public Task<bool> IsInRoleAsync(int userId, UserRole role) => throw new NotImplementedException();
        public Task<string> GeneratePasswordResetTokenAsync(int userId) => throw new NotImplementedException();
        public Task<bool> ValidatePasswordResetTokenAsync(int userId, string token) => throw new NotImplementedException();
        public Task LogSecurityEventAsync(SecurityEventType eventType, int? userId, string? username, string? ipAddress, string? userAgent, string? details = null) => throw new NotImplementedException();
        private Task<bool> RecordFailedLoginAttemptAsync(int userId) => throw new NotImplementedException();
        private Task<bool> ResetFailedLoginAttemptsAsync(int userId) => throw new NotImplementedException();
        private Task<bool> UpdateLastLoginDateAsync(int userId) => throw new NotImplementedException();
        private Task<bool> LockUserAccountAsync(int userId, DateTime lockUntil, int modifiedByUserId) => throw new NotImplementedException();
    }
}
