<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>نظام إدارة مؤسسة النور التربوي</AssemblyTitle>
    <AssemblyDescription>نظام إدارة شامل لمؤسسة النور التربوي</AssemblyDescription>
    <AssemblyCompany>مؤسسة النور التربوي</AssemblyCompany>
    <AssemblyProduct>Al-Noor Educational Institute Management System</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025 مؤسسة النور التربوي</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- إعدادات النشر -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>

    <!-- معلومات إضافية للتطبيق -->
    <ApplicationIcon Condition="Exists('Resources\app-icon.ico')">Resources\app-icon.ico</ApplicationIcon>
    <StartupObject>AlNoorEducationalInstitute.Program</StartupObject>
    <Win32Resource />
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="LiveChartsCore.SkiaSharpView.WinForms" Version="2.0.0-rc2" />
    <PackageReference Include="LiveChartsCore" Version="2.0.0-rc2" />
    <PackageReference Include="SkiaSharp.Views.WindowsForms" Version="2.88.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="108.0.3" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Quartz" Version="3.6.3" />
    <PackageReference Include="ClosedXML" Version="0.102.1" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Database\AlNoorDB.db">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
