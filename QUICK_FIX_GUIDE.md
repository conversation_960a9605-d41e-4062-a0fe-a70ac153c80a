# دليل الإصلاح السريع - Al-Noor Educational Institute

## 🎯 المشاكل الرئيسية وحلولها

### 1. مشكلة SQLite (الأهم)
**المشكلة**: `error CS1503: conversion impossible de 'System.Data.Common.DbDataReader' en 'System.Data.SQLite.SQLiteDataReader'`

**الحل**: تم تحديث المشروع لاستخدام `Microsoft.Data.Sqlite` بدلاً من `System.Data.SQLite`

**ما تم تغييره**:
- ✅ تحديث ملف المشروع (.csproj)
- ✅ تحديث using statements في DatabaseManager.cs
- ✅ تحديث using statements في AuthenticationService.cs
- ✅ تحديث using statements في StudentService.cs

### 2. مشكلة نماذج البيانات
**المشكلة**: `error CS1061: 'Student' ne contient pas de définition pour 'FirstName', 'LastName', 'PhoneNumber', 'IsActive'`

**الحل**: تم إضافة الخصائص المفقودة

**ما تم إضافته في Student.cs**:
- ✅ `FirstName`
- ✅ `LastName`
- ✅ `PhoneNumber`
- ✅ `IsActive`

**ما تم إضافته في Employee.cs**:
- ✅ `PhoneNumber` (alias لـ Phone)

### 3. مشكلة project.assets.json
**المشكلة**: `error NETSDK1005: Le fichier de composants 'project.assets.json' n'a aucune cible pour 'net6.0-windows'`

**الحل**: حذف مجلدات obj و bin وتشغيل dotnet restore

## 🚀 كيفية تطبيق الحلول

### الطريقة السريعة:
```cmd
test-build.bat
```

### الطريقة اليدوية:
```cmd
cd AlNoorEducationalInstitute
rmdir /s /q obj
rmdir /s /q bin
dotnet restore
dotnet build --configuration Release
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem
```

## 🔧 حلول للمشاكل المتبقية

### إذا ظهرت أخطاء تحويل:
```csharp
// خطأ
someObject.SomeId = reader["SomeIdColumn"];

// صحيح
someObject.SomeId = Convert.ToInt32(reader["SomeIdColumn"]);
```

### إذا ظهرت مشكلة Timer:
```csharp
// خطأ
Timer timer = new Timer();

// صحيح
System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
```

### إذا ظهرت مشاكل ILogger:
تأكد من وجود:
```csharp
using Microsoft.Extensions.Logging;
```

## 📋 الملفات المحدثة

### ملفات المشروع:
- ✅ AlNoorEducationalInstitute.csproj (تحديث الحزم)

### ملفات البيانات:
- ✅ Models/Student.cs (إضافة خصائص)
- ✅ Models/Employee.cs (إضافة PhoneNumber)
- ✅ Data/DatabaseManager.cs (تحديث SQLite)

### ملفات الخدمات:
- ✅ Services/AuthenticationService.cs (تحديث SQLite)
- ✅ Services/StudentService.cs (تحديث SQLite)

### ملفات مساعدة:
- ✅ test-build.bat (اختبار البناء)
- ✅ fix-restore.bat (إصلاح مشكلة الاستعادة)
- ✅ Update-SQLiteReferences.ps1 (تحديث المراجع)

## 🎯 النتيجة المتوقعة

بعد تطبيق هذه الحلول:
- ✅ 0 أخطاء تجميع
- ⚠️ تحذيرات قليلة فقط (غير مؤثرة)
- ✅ تطبيق جاهز في: `Published\AlNoorSystem\AlNoorEducationalInstitute.exe`

## 🔑 بيانات الدخول الافتراضية
- **المستخدم**: admin
- **كلمة المرور**: admin123

## 📞 إذا استمرت المشاكل
1. تأكد من تثبيت .NET 6.0 SDK
2. تشغيل Visual Studio كمدير
3. استخدام Visual Studio بدلاً من سطر الأوامر
4. حذف مجلد المشروع وإعادة استخراجه
