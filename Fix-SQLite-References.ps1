#!/usr/bin/env pwsh
# سكريبت شامل لتحديث جميع مراجع SQLite

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  إصلاح مراجع SQLite - الحل النهائي" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# قائمة الملفات المطلوب تحديثها
$files = @(
    "AlNoorEducationalInstitute\Data\DatabaseManager.cs",
    "AlNoorEducationalInstitute\Services\AuthenticationService.cs",
    "AlNoorEducationalInstitute\Services\StudentService.cs",
    "AlNoorEducationalInstitute\Services\EmployeeService.cs",
    "AlNoorEducationalInstitute\Services\ClassService.cs",
    "AlNoorEducationalInstitute\Services\SubjectService.cs",
    "AlNoorEducationalInstitute\Services\InstitutionService.cs",
    "AlNoorEducationalInstitute\Services\FeeService.cs",
    "AlNoorEducationalInstitute\Services\InvoiceService.cs",
    "AlNoorEducationalInstitute\Services\PaymentService.cs",
    "AlNoorEducationalInstitute\Services\GradeService.cs",
    "AlNoorEducationalInstitute\Services\AttendanceService.cs",
    "AlNoorEducationalInstitute\Services\ReportService.cs"
)

# قائمة التحديثات المطلوبة
$replacements = @{
    'using System\.Data\.SQLite;' = 'using Microsoft.Data.Sqlite;'
    '\bSQLiteConnection\b' = 'SqliteConnection'
    '\bSQLiteCommand\b' = 'SqliteCommand'
    '\bSQLiteDataReader\b' = 'SqliteDataReader'
    '\bSQLiteParameter\b' = 'SqliteParameter'
    '\bSQLiteTransaction\b' = 'SqliteTransaction'
    '\bSQLiteConnectionStringBuilder\b' = 'SqliteConnectionStringBuilder'
}

$totalFiles = 0
$updatedFiles = 0
$totalReplacements = 0

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "معالجة: $file" -ForegroundColor Yellow
        $totalFiles++
        
        $content = Get-Content $file -Raw -Encoding UTF8
        $originalContent = $content
        $fileReplacements = 0
        
        # تطبيق جميع التحديثات باستخدام regex
        foreach ($pattern in $replacements.Keys) {
            $replacement = $replacements[$pattern]
            $matches = [regex]::Matches($content, $pattern)
            if ($matches.Count -gt 0) {
                $content = [regex]::Replace($content, $pattern, $replacement)
                $fileReplacements += $matches.Count
                Write-Host "  ✓ استبدال $($matches.Count) من '$pattern'" -ForegroundColor Green
            }
        }
        
        # حفظ الملف إذا تم تغييره
        if ($content -ne $originalContent) {
            Set-Content $file -Value $content -NoNewline -Encoding UTF8
            Write-Host "  ✓ تم حفظ $fileReplacements تحديث في الملف" -ForegroundColor Green
            $updatedFiles++
            $totalReplacements += $fileReplacements
        } else {
            Write-Host "  - لا يحتاج تحديث" -ForegroundColor Gray
        }
        Write-Host ""
    } else {
        Write-Host "✗ ملف غير موجود: $file" -ForegroundColor Red
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  ملخص العملية" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "الملفات المعالجة: $totalFiles" -ForegroundColor White
Write-Host "الملفات المحدثة: $updatedFiles" -ForegroundColor Green
Write-Host "إجمالي التحديثات: $totalReplacements" -ForegroundColor Green

if ($updatedFiles -gt 0) {
    Write-Host ""
    Write-Host "✓ تم تحديث مراجع SQLite بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "الخطوات التالية:" -ForegroundColor Yellow
    Write-Host "1. cd AlNoorEducationalInstitute" -ForegroundColor White
    Write-Host "2. rmdir /s /q obj" -ForegroundColor White
    Write-Host "3. rmdir /s /q bin" -ForegroundColor White
    Write-Host "4. dotnet restore" -ForegroundColor White
    Write-Host "5. dotnet build --configuration Release" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "جميع الملفات محدثة مسبقاً" -ForegroundColor Gray
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
