@echo off
echo ========================================
echo   اختبار سريع للبناء
echo ========================================
echo.

echo إعداد PATH...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo الانتقال للمشروع...
cd AlNoorEducationalInstitute

echo حذف ملفات البناء القديمة...
if exist "obj" rmdir /s /q obj
if exist "bin" rmdir /s /q bin

echo استعادة الحزم...
dotnet restore --verbosity quiet

echo اختبار البناء...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ نجح البناء! لا توجد أخطاء تجميع
    echo.
    echo تشغيل النشر...
    dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem --verbosity quiet
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ نجح النشر! التطبيق جاهز
        echo.
        echo 📁 الموقع: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
        echo 🔑 المستخدم: admin | كلمة المرور: admin123
    else (
        echo ✗ فشل النشر
    )
) else (
    echo.
    echo ✗ فشل البناء - ما زالت هناك أخطاء
    echo.
    echo تشغيل البناء مع تفاصيل الأخطاء...
    dotnet build --configuration Release --verbosity normal
)

cd ..
echo.
pause
