@echo off
echo ========================================
echo   Al-Noor - Fix Package Restore Issue
echo ========================================
echo.

echo The issue: project.assets.json still contains net9.0-windows target
echo Solution: Run dotnet restore to regenerate with net6.0-windows
echo.

echo Step 1: Setting PATH...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo Step 2: Going to project directory...
cd AlNoorEducationalInstitute

echo Step 3: Deleting old cache files...
if exist "obj" (
    echo Deleting obj folder...
    rmdir /s /q obj
)
if exist "bin" (
    echo Deleting bin folder...
    rmdir /s /q bin
)

echo Step 4: Running dotnet restore...
echo This will regenerate project.assets.json with correct net6.0-windows target
dotnet restore --verbosity normal

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Restore failed
    pause
    exit /b 1
)

echo ✓ Restore successful!
echo.

echo Step 5: Building project...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo ✓ Build successful!
echo.

echo Step 6: Publishing application...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   SUCCESS! Problem Fixed!
    echo ========================================
    echo.
    echo The project.assets.json has been regenerated with net6.0-windows
    echo Application published to: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    echo.
    echo Default login:
    echo Username: admin
    echo Password: admin123
) else (
    echo ERROR: Publish failed
)

cd ..
echo.
pause
