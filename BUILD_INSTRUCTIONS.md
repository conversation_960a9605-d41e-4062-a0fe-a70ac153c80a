# Al-Noor Educational Institute - Build Instructions

## 🔍 Current Issue
Your system has **.NET 6.0.428** but the project was targeting **.NET 9.0**, causing compatibility error `NETSDK1045`.

## ✅ What I've Fixed
1. **Changed target framework** from `net9.0-windows` to `net6.0-windows`
2. **Updated package versions** to be compatible with .NET 6.0
3. **Created multiple build scripts** for different scenarios

## 🚀 Build Options

### Option 1: <PERSON><PERSON> Script (Recommended)
```cmd
build-net6.bat
```

### Option 2: PowerShell Script
```powershell
PowerShell -ExecutionPolicy Bypass -File Build-AlNoor.ps1
```

### Option 3: Manual Commands
Open Command Prompt and run:
```cmd
cd "C:\Users\<USER>\OneDrive\Bureau\YOP\AlNoorEducationalInstitute"
dotnet clean
dotnet restore
dotnet build --configuration Release
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem
```

### Option 4: Visual Studio
1. Download Visual Studio Community (free)
2. Open `AlNoorEducationalInstitute.sln`
3. Build > Rebuild Solution
4. Build > Publish

## 🔧 Troubleshooting

### If .NET is not found:
```cmd
set "PATH=%PATH%;C:\Program Files\dotnet"
dotnet --version
```

### If packages fail to restore:
```cmd
dotnet restore --force
dotnet nuget locals all --clear
dotnet restore
```

### If build still fails:
1. Check error messages carefully
2. Try cleaning: `dotnet clean`
3. Delete `bin` and `obj` folders
4. Run `dotnet restore` again

## 📁 Expected Output
After successful build:
```
Published\AlNoorSystem\AlNoorEducationalInstitute.exe
```

## 🔑 Default Login
- **Username:** admin
- **Password:** admin123

## 🎯 Verification
Run `check-build.bat` to verify if the application was built successfully.

## 📞 Alternative Solutions

### Install .NET 9.0 SDK (Future-proof)
1. Download from: https://dotnet.microsoft.com/download/dotnet/9.0
2. Install SDK (not just runtime)
3. Revert project to .NET 9.0 if desired

### Use Existing Published Version
If there's already a working version, check:
```
Published\AlNoorSystem\AlNoorEducationalInstitute.exe
```

## 🔄 Package Versions Used
- Target Framework: .NET 6.0
- Microsoft.Extensions.*: 6.0.x
- RestSharp: 108.0.3 (compatible with .NET 6.0)
- Quartz: 3.6.3 (compatible with .NET 6.0)
- Other packages: Latest compatible versions

## 📝 Notes
- The application functionality remains the same
- Only the target framework and package versions were updated
- All features should work identically
- Performance should be similar or better with .NET 6.0
