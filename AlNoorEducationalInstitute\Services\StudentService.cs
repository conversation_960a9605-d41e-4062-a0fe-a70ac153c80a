using System;
using System.Collections.Generic;
using Microsoft.Data.Sqlite;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AlNoorEducationalInstitute.Data;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// خدمة إدارة الطلاب
    /// Student management service
    /// </summary>
    public class StudentService : IStudentService
    {
        private readonly DatabaseManager _databaseManager;
        private readonly ILogger<StudentService> _logger;

        public StudentService(DatabaseManager databaseManager, ILogger<StudentService> logger)
        {
            _databaseManager = databaseManager;
            _logger = logger;
        }

        public async Task<IEnumerable<Student>> GetAllStudentsAsync()
        {
            var students = new List<Student>();
            
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT s.*, c.ClassName 
                    FROM Students s
                    LEFT JOIN Classes c ON s.CurrentClassId = c.ClassId
                    ORDER BY s.FullNameArabic";

                using var command = new SQLiteCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    students.Add(MapReaderToStudent(reader));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع الطلاب");
                throw;
            }

            return students;
        }

        public async Task<Student?> GetStudentByIdAsync(int studentId)
        {
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT s.*, c.ClassName 
                    FROM Students s
                    LEFT JOIN Classes c ON s.CurrentClassId = c.ClassId
                    WHERE s.StudentId = @StudentId";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@StudentId", studentId);
                
                using var reader = await command.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    return MapReaderToStudent(reader);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الطالب بالمعرف {StudentId}", studentId);
                throw;
            }

            return null;
        }

        public async Task<Student?> GetStudentByNumberAsync(string studentNumber)
        {
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT s.*, c.ClassName 
                    FROM Students s
                    LEFT JOIN Classes c ON s.CurrentClassId = c.ClassId
                    WHERE s.StudentNumber = @StudentNumber";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@StudentNumber", studentNumber);
                
                using var reader = await command.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    return MapReaderToStudent(reader);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الطالب بالرقم {StudentNumber}", studentNumber);
                throw;
            }

            return null;
        }

        public async Task<IEnumerable<Student>> SearchStudentsByNameAsync(string name)
        {
            var students = new List<Student>();
            
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT s.*, c.ClassName 
                    FROM Students s
                    LEFT JOIN Classes c ON s.CurrentClassId = c.ClassId
                    WHERE s.FullNameArabic LIKE @Name OR s.FullNameEnglish LIKE @Name
                    ORDER BY s.FullNameArabic";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@Name", $"%{name}%");
                
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    students.Add(MapReaderToStudent(reader));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الطلاب بالاسم {Name}", name);
                throw;
            }

            return students;
        }

        public async Task<IEnumerable<Student>> GetStudentsByClassAsync(int classId)
        {
            var students = new List<Student>();
            
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT s.*, c.ClassName 
                    FROM Students s
                    LEFT JOIN Classes c ON s.CurrentClassId = c.ClassId
                    WHERE s.CurrentClassId = @ClassId
                    ORDER BY s.FullNameArabic";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@ClassId", classId);
                
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    students.Add(MapReaderToStudent(reader));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على طلاب الفصل {ClassId}", classId);
                throw;
            }

            return students;
        }

        public async Task<IEnumerable<Student>> GetStudentsByStatusAsync(StudentStatus status)
        {
            var students = new List<Student>();
            
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT s.*, c.ClassName 
                    FROM Students s
                    LEFT JOIN Classes c ON s.CurrentClassId = c.ClassId
                    WHERE s.Status = @Status
                    ORDER BY s.FullNameArabic";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@Status", (int)status);
                
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    students.Add(MapReaderToStudent(reader));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الطلاب بالحالة {Status}", status);
                throw;
            }

            return students;
        }

        public async Task<int> AddStudentAsync(Student student)
        {
            try
            {
                using var connection = _databaseManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    INSERT INTO Students (
                        StudentNumber, FullNameArabic, FullNameEnglish, DateOfBirth, PlaceOfBirth,
                        Gender, Nationality, GuardianName, GuardianRelationship, GuardianOccupation,
                        GuardianPhone, GuardianPhone2, GuardianEmail, Address, CurrentClassId,
                        EnrollmentDate, Status, Notes, CreatedDate, LastModifiedDate,
                        CreatedByUserId, LastModifiedByUserId
                    ) VALUES (
                        @StudentNumber, @FullNameArabic, @FullNameEnglish, @DateOfBirth, @PlaceOfBirth,
                        @Gender, @Nationality, @GuardianName, @GuardianRelationship, @GuardianOccupation,
                        @GuardianPhone, @GuardianPhone2, @GuardianEmail, @Address, @CurrentClassId,
                        @EnrollmentDate, @Status, @Notes, @CreatedDate, @LastModifiedDate,
                        @CreatedByUserId, @LastModifiedByUserId
                    );
                    SELECT last_insert_rowid();";

                using var command = new SQLiteCommand(sql, connection);
                AddStudentParameters(command, student);
                
                var result = await command.ExecuteScalarAsync();
                var studentId = Convert.ToInt32(result);

                // تحديث عدد الطلاب في الفصل إذا كان الطالب مسجل في فصل
                if (student.CurrentClassId.HasValue)
                {
                    await UpdateClassStudentCountAsync(connection, student.CurrentClassId.Value);
                }

                _logger.LogInformation("تم إضافة طالب جديد بالمعرف {StudentId}", studentId);
                return studentId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة طالب جديد");
                throw;
            }
        }

        // باقي الدوال سيتم إضافتها في الجزء التالي...
        
        private Student MapReaderToStudent(SQLiteDataReader reader)
        {
            return new Student
            {
                StudentId = reader.GetInt32("StudentId"),
                StudentNumber = reader.GetString("StudentNumber"),
                FullNameArabic = reader.GetString("FullNameArabic"),
                FullNameEnglish = reader.IsDBNull("FullNameEnglish") ? null : reader.GetString("FullNameEnglish"),
                DateOfBirth = reader.GetDateTime("DateOfBirth"),
                PlaceOfBirth = reader.IsDBNull("PlaceOfBirth") ? null : reader.GetString("PlaceOfBirth"),
                Gender = (Gender)reader.GetInt32("Gender"),
                Nationality = reader.IsDBNull("Nationality") ? null : reader.GetString("Nationality"),
                GuardianName = reader.GetString("GuardianName"),
                GuardianRelationship = reader.IsDBNull("GuardianRelationship") ? null : reader.GetString("GuardianRelationship"),
                GuardianOccupation = reader.IsDBNull("GuardianOccupation") ? null : reader.GetString("GuardianOccupation"),
                GuardianPhone = reader.GetString("GuardianPhone"),
                GuardianPhone2 = reader.IsDBNull("GuardianPhone2") ? null : reader.GetString("GuardianPhone2"),
                GuardianEmail = reader.IsDBNull("GuardianEmail") ? null : reader.GetString("GuardianEmail"),
                Address = reader.GetString("Address"),
                CurrentClassId = reader.IsDBNull("CurrentClassId") ? null : reader.GetInt32("CurrentClassId"),
                EnrollmentDate = reader.GetDateTime("EnrollmentDate"),
                Status = (StudentStatus)reader.GetInt32("Status"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                LastModifiedDate = reader.GetDateTime("LastModifiedDate"),
                CreatedByUserId = reader.GetInt32("CreatedByUserId"),
                LastModifiedByUserId = reader.GetInt32("LastModifiedByUserId")
            };
        }

        private void AddStudentParameters(SQLiteCommand command, Student student)
        {
            command.Parameters.AddWithValue("@StudentNumber", student.StudentNumber);
            command.Parameters.AddWithValue("@FullNameArabic", student.FullNameArabic);
            command.Parameters.AddWithValue("@FullNameEnglish", student.FullNameEnglish ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DateOfBirth", student.DateOfBirth);
            command.Parameters.AddWithValue("@PlaceOfBirth", student.PlaceOfBirth ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Gender", (int)student.Gender);
            command.Parameters.AddWithValue("@Nationality", student.Nationality ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@GuardianName", student.GuardianName);
            command.Parameters.AddWithValue("@GuardianRelationship", student.GuardianRelationship ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@GuardianOccupation", student.GuardianOccupation ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@GuardianPhone", student.GuardianPhone);
            command.Parameters.AddWithValue("@GuardianPhone2", student.GuardianPhone2 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@GuardianEmail", student.GuardianEmail ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Address", student.Address);
            command.Parameters.AddWithValue("@CurrentClassId", student.CurrentClassId ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@EnrollmentDate", student.EnrollmentDate);
            command.Parameters.AddWithValue("@Status", (int)student.Status);
            command.Parameters.AddWithValue("@Notes", student.Notes ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@CreatedDate", student.CreatedDate);
            command.Parameters.AddWithValue("@LastModifiedDate", student.LastModifiedDate);
            command.Parameters.AddWithValue("@CreatedByUserId", student.CreatedByUserId);
            command.Parameters.AddWithValue("@LastModifiedByUserId", student.LastModifiedByUserId);
        }

        private async Task UpdateClassStudentCountAsync(SQLiteConnection connection, int classId)
        {
            var sql = @"
                UPDATE Classes 
                SET CurrentStudentCount = (
                    SELECT COUNT(*) 
                    FROM Students 
                    WHERE CurrentClassId = @ClassId AND Status = 1
                )
                WHERE ClassId = @ClassId";

            using var command = new SQLiteCommand(sql, connection);
            command.Parameters.AddWithValue("@ClassId", classId);
            await command.ExecuteNonQueryAsync();
        }

        // سيتم إضافة باقي الدوال في الجزء التالي
        public Task<bool> UpdateStudentAsync(Student student) => throw new NotImplementedException();
        public Task<bool> DeleteStudentAsync(int studentId) => throw new NotImplementedException();
        public Task<bool> ArchiveStudentAsync(int studentId, StudentStatus newStatus, int userId) => throw new NotImplementedException();
        public Task<bool> TransferStudentToClassAsync(int studentId, int newClassId, int userId) => throw new NotImplementedException();
        public Task<bool> IsStudentNumberExistsAsync(string studentNumber, int? excludeStudentId = null) => throw new NotImplementedException();
        public Task<StudentStatistics> GetStudentStatisticsAsync() => throw new NotImplementedException();
        public Task<IEnumerable<Student>> GetStudentsEnrolledBetweenAsync(DateTime startDate, DateTime endDate) => throw new NotImplementedException();
        public Task<byte[]> ExportStudentsToExcelAsync(IEnumerable<int>? studentIds = null) => throw new NotImplementedException();
        public Task<ImportResult> ImportStudentsFromExcelAsync(byte[] fileData, int userId) => throw new NotImplementedException();
    }
}
