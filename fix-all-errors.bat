@echo off
echo ========================================
echo   Al-Noor - إصلاح جميع أخطاء التجميع
echo ========================================
echo.

echo الخطوة 1: تحديث مراجع SQLite...
echo تشغيل سكريبت PowerShell لتحديث المراجع...
PowerShell -ExecutionPolicy Bypass -File Update-SQLiteReferences.ps1

echo.
echo الخطوة 2: حذف ملفات البناء القديمة...
cd AlNoorEducationalInstitute
if exist "obj" (
    echo حذف مجلد obj...
    rmdir /s /q obj
)
if exist "bin" (
    echo حذف مجلد bin...
    rmdir /s /q bin
)

echo.
echo الخطوة 3: إعداد PATH لـ .NET...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo.
echo الخطوة 4: استعادة الحزم...
dotnet restore --verbosity normal

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: فشل في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo الخطوة 5: بناء المشروع...
dotnet build --configuration Release --verbosity normal

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: فشل في بناء المشروع
    echo.
    echo تحقق من الأخطاء أعلاه وقم بإصلاحها يدوياً:
    echo 1. أخطاء الخصائص المفقودة في Student.cs و Employee.cs
    echo 2. أخطاء التحويل (استخدم Convert.ToInt32)
    echo 3. مشاكل Timer (استخدم System.Windows.Forms.Timer)
    pause
    exit /b 1
)

echo.
echo الخطوة 6: نشر التطبيق...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   نجح! تم إصلاح المشاكل
    echo ========================================
    echo.
    echo التطبيق جاهز في: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    echo.
    echo بيانات الدخول الافتراضية:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
) else (
    echo ERROR: فشل في نشر التطبيق
    echo تحقق من الأخطاء أعلاه
)

cd ..
echo.
pause
