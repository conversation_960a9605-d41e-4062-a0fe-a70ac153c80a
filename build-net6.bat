@echo off
echo ========================================
echo   Al-Noor Educational Institute
echo   .NET 6.0 Compatible Build
echo ========================================
echo.

echo Checking .NET 6.0 installation...
echo .NET Version:
dotnet --version

echo.
echo Project has been updated to target .NET 6.0
echo This should work with your current installation.
echo.

echo Step 1: Setting PATH...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo Step 2: Cleaning project...
cd AlNoorEducationalInstitute
dotnet clean --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

echo Step 3: Restoring packages...
dotnet restore --verbosity normal

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)

echo Step 4: Building project...
dotnet build --configuration Release --no-restore

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    echo.
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo Step 5: Publishing application...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   SUCCESS! Application Published
    echo ========================================
    echo.
    echo Location: Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    echo.
    echo Default login:
    echo Username: admin
    echo Password: admin123
    echo.
    echo You can now run the application!
) else (
    echo ERROR: Publish failed
)

cd ..
echo.
pause
