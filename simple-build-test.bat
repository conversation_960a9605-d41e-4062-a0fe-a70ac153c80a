@echo off
echo ========================================
echo   Simple Build Test
echo ========================================
echo.

echo Step 1: Setting PATH...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo Step 2: Testing dotnet...
"C:\Program Files\dotnet\dotnet.exe" --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: dotnet not working
    pause
    exit /b 1
)

echo Step 3: Going to project directory...
cd AlNoorEducationalInstitute

echo Step 4: Restoring packages...
"C:\Program Files\dotnet\dotnet.exe" restore --verbosity minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Restore failed
    pause
    exit /b 1
)

echo Step 5: Building project...
"C:\Program Files\dotnet\dotnet.exe" build --no-restore --verbosity minimal
if %ERRORLEVEL% EQU 0 (
    echo ✓ Build successful!
    echo.
    echo Step 6: Publishing...
    "C:\Program Files\dotnet\dotnet.exe" publish --no-build --configuration Release --output ..\Published\AlNoorSystem
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Publish successful!
        echo Application is ready at: ..\Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    ) else (
        echo ✗ Publish failed
    )
) else (
    echo ✗ Build failed
)

echo.
pause
