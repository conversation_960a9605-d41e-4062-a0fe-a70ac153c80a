@echo off
echo ========================================
echo   الحل النهائي الشامل
echo   Al-Noor Educational Institute
echo ========================================
echo.

echo الخطوة 1: تحديث مراجع SQLite في جميع الملفات...
echo تشغيل سكريبت PowerShell...
PowerShell -ExecutionPolicy Bypass -File Fix-SQLite-References.ps1

echo.
echo الخطوة 2: تنظيف ملفات البناء القديمة...
cd AlNoorEducationalInstitute

if exist "obj" (
    echo حذف مجلد obj...
    rmdir /s /q obj
)

if exist "bin" (
    echo حذف مجلد bin...
    rmdir /s /q bin
)

echo.
echo الخطوة 3: إعداد بيئة .NET...
set "PATH=%PATH%;C:\Program Files\dotnet"

echo.
echo الخطوة 4: استعادة الحزم...
dotnet restore --verbosity normal

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ✗ فشل في استعادة الحزم
    echo تحقق من اتصال الإنترنت وإعدادات NuGet
    pause
    exit /b 1
)

echo.
echo الخطوة 5: بناء المشروع...
dotnet build --configuration Release --verbosity normal

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ✗ فشل في بناء المشروع
    echo.
    echo الأخطاء المحتملة المتبقية:
    echo 1. أخطاء التحويل - استخدم Convert.ToInt32()
    echo 2. مشاكل Timer - استخدم System.Windows.Forms.Timer
    echo 3. مشاكل ILogger - تأكد من using Microsoft.Extensions.Logging
    echo.
    pause
    exit /b 1
)

echo.
echo ✓ نجح البناء! الآن ننشر التطبيق...
echo.

echo الخطوة 6: نشر التطبيق...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ..\Published\AlNoorSystem --verbosity normal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   🎉 نجح! تم حل جميع المشاكل
    echo ========================================
    echo.
    echo 📁 موقع التطبيق:
    echo    Published\AlNoorSystem\AlNoorEducationalInstitute.exe
    echo.
    echo 🔑 بيانات الدخول الافتراضية:
    echo    المستخدم: admin
    echo    كلمة المرور: admin123
    echo.
    echo 🚀 يمكنك الآن تشغيل التطبيق!
    echo.
) else (
    echo.
    echo ✗ فشل في نشر التطبيق
    echo تحقق من الأخطاء أعلاه
)

cd ..
echo.
echo اضغط أي مفتاح للخروج...
pause > nul
